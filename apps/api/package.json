{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@langchain/core": "^0.3.57", "@langchain/deepseek": "^0.0.1", "@langchain/langgraph": "^0.2.74", "@langchain/openai": "^0.5.11", "@langchain/textsplitters": "^0.1.0", "@nestjs/bullmq": "^10.2.3", "@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/platform-express": "^11.1.2", "@notionhq/client": "^3.1.3", "@supabase/supabase-js": "^2.49.9", "@tavily/core": "^0.3.7", "@types/cheerio": "^1.0.0", "@types/fs-extra": "^11.0.4", "@types/string-similarity": "^4.0.2", "axios": "^1.9.0", "bullmq": "^5.53.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "isomorphic-git": "^1.30.2", "langchain": "^0.3.27", "libsodium-wrappers": "^0.7.15", "notion-to-md": "^3.1.9", "octokit": "^3.2.2", "openai": "^4.104.0", "reflect-metadata": "^0.2.2", "remark-stringify": "^11.0.0", "rxjs": "^7.8.2", "slugify": "^1.6.6", "string-similarity": "^4.0.4", "unified": "^11.0.5", "yaml": "^2.8.0", "zod": "^3.25.50", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.2", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.29", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/libsodium-wrappers": "^0.7.14", "@types/node": "^22.15.29", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "globals": "^16.2.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}