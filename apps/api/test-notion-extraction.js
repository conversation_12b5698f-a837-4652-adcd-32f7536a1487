// @ts-check

// Try to import Notion packages if available
const notionClient = require('@notionhq/client');
const { default: axios } = require('axios');
const Client = notionClient.Client;
const notionToMd = require('notion-to-md');
const NotionToMarkdown = notionToMd.NotionToMarkdown;

require('dotenv').config();

// Real working Notion extraction implementation
async function testRealNotionExtraction(notionUrl) {
    console.log('\n🔧 Testing REAL Notion extraction implementation...');

    // Extract page ID from Notion URL
    const pageId = extractNotionPageId(notionUrl);
    if (!pageId) {
        console.log('❌ Could not extract page ID from URL');
        return null;
    }

}

// Extract page ID from various Notion URL formats
function extractNotionPageId(url) {
    // Handle different Notion URL formats:
    // https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278
    // https://notion.so/username/Page-Name-54c7c618172b4026a40ea94e584d0278

    const patterns = [
        /([a-f0-9]{32})/i,  // 32 character hex string
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i,  // UUID format
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            let id = match[1];
            // Convert to UUID format if needed
            if (id.length === 32) {
                id = `${id.slice(0, 8)}-${id.slice(8, 12)}-${id.slice(12, 16)}-${id.slice(16, 20)}-${id.slice(20)}`;
            }
            return id;
        }
    }

    return null;
}



async function extractWithUnofficial(url, pageId) {
    // This would use unofficial Notion API or scraping methods
    // For now, let's try a simple fetch to the public API endpoint

    const publicApiUrl = `https://notion-api.splitbee.io/v1/page/${pageId}`;

    try {
        const response = await axios.get(publicApiUrl, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; NotionExtractor/1.0)',
            }
        });

        if (response.data && typeof response.data === 'object') {
            // Debug: Log the structure to understand the data format
            console.log('🔍 Debugging Notion data structure...');
            console.log('Top-level keys:', Object.keys(response.data));

            // Parse the Notion data structure to extract meaningful content
            const parsedContent = parseNotionContent(response.data);
            if (parsedContent && parsedContent.length > 100) {
                return parsedContent;
            }

            // Fallback to JSON if parsing fails
            return JSON.stringify(response.data, null, 2);
        }

        throw new Error('No valid data received');
    } catch (error) {
        throw new Error(`Unofficial API failed: ${error.message}`);
    }
}


// Run the tests
async function runTests() {
    console.log('🚀 Starting Notion Integration Tests\n');

    await testRealNotionExtraction('https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278');
}


runTests()
    .catch(console.error);
